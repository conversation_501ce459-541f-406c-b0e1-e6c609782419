#!/usr/bin/env python3
"""
Complete system integration test for EduSarathi
Tests all services and modules with dummy data
"""

import requests
import json
import time
import sys
from pathlib import Path

# Service URLs
AI_SERVICE_URL = "http://localhost:8001"
BACKEND_SERVICE_URL = "http://localhost:5001"
FRONTEND_SERVICE_URL = "http://localhost:3000"

def test_service_health(service_name, url, endpoint=""):
    """Test if a service is healthy and responding"""
    try:
        response = requests.get(f"{url}{endpoint}", timeout=5)
        if response.status_code == 200:
            print(f"✅ {service_name} is healthy")
            return True
        else:
            print(f"⚠️  {service_name} responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {service_name} is not responding: {e}")
        return False

def test_ai_service():
    """Test AI service endpoints"""
    print("\n=== Testing AI Service ===")
    
    # Test health endpoint
    if not test_service_health("AI Service", AI_SERVICE_URL, "/health"):
        return False
    
    # Test quiz generation
    try:
        quiz_data = {
            "subject": "Physics",
            "topic": "Laws of Motion",
            "grade": 11,
            "language": "en",
            "questionCount": 3,
            "difficulty": "medium",
            "questionTypes": ["mcq"]
        }
        
        response = requests.post(f"{AI_SERVICE_URL}/quiz/generate",
                               json=quiz_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and 'questions' in result['data']:
                print(f"✅ Quiz generation working - generated {len(result['data']['questions'])} questions")
                return True
            else:
                print("⚠️  Quiz generation returned unexpected format")
                print(f"Response: {result}")
                return False
        else:
            print(f"❌ Quiz generation failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Quiz generation request failed: {e}")
        return False

def test_backend_service():
    """Test backend service endpoints"""
    print("\n=== Testing Backend Service ===")
    
    # Test basic connectivity
    if not test_service_health("Backend Service", BACKEND_SERVICE_URL):
        return False
    
    # Test API endpoints (if available)
    try:
        # Try to get subjects or any available endpoint
        response = requests.get(f"{BACKEND_SERVICE_URL}/api/subjects", timeout=10)
        if response.status_code in [200, 404]:  # 404 is ok if endpoint doesn't exist
            print("✅ Backend API is responding")
            return True
        else:
            print(f"⚠️  Backend API responded with status {response.status_code}")
            return True  # Still consider it working
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Backend API test failed: {e}")
        return True  # Backend might be working but without specific endpoints

def test_frontend_service():
    """Test frontend service"""
    print("\n=== Testing Frontend Service ===")
    
    # Test if frontend is serving content
    try:
        response = requests.get(FRONTEND_SERVICE_URL, timeout=10)
        if response.status_code == 200:
            if "EduSarathi" in response.text or "html" in response.text.lower():
                print("✅ Frontend is serving content")
                return True
            else:
                print("⚠️  Frontend is responding but content unclear")
                return True
        else:
            print(f"❌ Frontend responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend is not responding: {e}")
        return False

def test_dummy_data_integration():
    """Test that dummy data is properly integrated"""
    print("\n=== Testing Dummy Data Integration ===")
    
    dummy_data_dir = Path('ai/dummy_data')
    required_files = ['quizzes.json', 'slides.json', 'mindmaps.json', 
                     'lecture_plans.json', 'curriculum.json']
    
    all_files_exist = True
    for file_name in required_files:
        file_path = dummy_data_dir / file_name
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"✅ {file_name} - loaded successfully")
            except json.JSONDecodeError:
                print(f"❌ {file_name} - invalid JSON format")
                all_files_exist = False
        else:
            print(f"❌ {file_name} - file not found")
            all_files_exist = False
    
    return all_files_exist

def test_multilingual_support():
    """Test multilingual support with dummy data"""
    print("\n=== Testing Multilingual Support ===")
    
    try:
        # Test Hindi quiz generation
        quiz_data = {
            "subject": "भौतिक विज्ञान",
            "topic": "गति के नियम",
            "grade": 11,
            "language": "hi",
            "questionCount": 2,
            "difficulty": "medium",
            "questionTypes": ["mcq"]
        }
        
        response = requests.post(f"{AI_SERVICE_URL}/quiz/generate",
                               json=quiz_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and 'questions' in result['data']:
                print("✅ Multilingual support working - Hindi quiz generated")
                return True
            else:
                print("⚠️  Multilingual test returned unexpected format")
                print(f"Response: {result}")
                return False
        else:
            print(f"⚠️  Multilingual test failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Multilingual test request failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting EduSarathi Complete System Test")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("AI Service", test_ai_service),
        ("Backend Service", test_backend_service),
        ("Frontend Service", test_frontend_service),
        ("Dummy Data Integration", test_dummy_data_integration),
        ("Multilingual Support", test_multilingual_support)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SYSTEM TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("🌐 EduSarathi is ready for use!")
        print(f"📱 Frontend: {FRONTEND_SERVICE_URL}")
        print(f"🔧 Backend: {BACKEND_SERVICE_URL}")
        print(f"🤖 AI Service: {AI_SERVICE_URL}")
        return True
    else:
        print(f"\n⚠️  {total - passed} issues found. Please check the failed tests.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
