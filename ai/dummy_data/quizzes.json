{"quizzes": [{"id": "quiz_physics_11_motion_en", "title": "Laws of Motion - Chapter Test", "description": "Comprehensive quiz on Newton's laws of motion and their applications", "subject": "Physics", "topic": "Laws of Motion", "grade": "11", "language": "en", "difficulty": "medium", "totalPoints": 25, "settings": {"timeLimit": 45, "attemptsAllowed": 2, "shuffleQuestions": true, "shuffleOptions": true, "showResults": "after_submission", "passingScore": 60}, "questions": [{"type": "mcq", "question": "Which of <PERSON>'s laws explains why a passenger moves forward when a car suddenly stops?", "options": [{"text": "First Law (Law of Inertia)", "isCorrect": true}, {"text": "Second Law (F = ma)", "isCorrect": false}, {"text": "Third Law (Action-Reaction)", "isCorrect": false}, {"text": "Law of Universal Gravitation", "isCorrect": false}], "correctAnswer": "First Law (Law of Inertia)", "explanation": "The first law states that an object in motion tends to stay in motion unless acted upon by an external force. The passenger continues moving forward due to inertia.", "points": 2, "difficulty": "easy", "tags": ["newton", "inertia", "motion"]}, {"type": "mcq", "question": "If a force of 20 N is applied to an object with mass 4 kg, what is its acceleration?", "options": [{"text": "5 m/s²", "isCorrect": true}, {"text": "80 m/s²", "isCorrect": false}, {"text": "0.2 m/s²", "isCorrect": false}, {"text": "16 m/s²", "isCorrect": false}], "correctAnswer": "5 m/s²", "explanation": "Using <PERSON>'s second law: F = ma, so a = F/m = 20N/4kg = 5 m/s²", "points": 3, "difficulty": "medium", "tags": ["newton", "force", "acceleration"]}, {"type": "true_false", "question": "According to <PERSON>'s third law, the force exerted by object A on object B is equal in magnitude but opposite in direction to the force exerted by object B on object A.", "correctAnswer": "true", "explanation": "This is the exact statement of <PERSON>'s third law - for every action, there is an equal and opposite reaction.", "points": 2, "difficulty": "easy", "tags": ["newton", "action-reaction"]}, {"type": "short_answer", "question": "What is the SI unit of force?", "correctAnswer": "<PERSON>", "explanation": "The SI unit of force is <PERSON> (N), named after Sir <PERSON>.", "points": 1, "difficulty": "easy", "tags": ["units", "force"]}, {"type": "fill_blank", "question": "<PERSON>'s _____ law states that F = ma.", "correctAnswer": "second", "explanation": "<PERSON>'s second law establishes the relationship between force, mass, and acceleration.", "points": 1, "difficulty": "easy", "tags": ["newton", "laws"]}], "tags": ["physics", "motion", "newton", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}}, {"id": "quiz_physics_11_motion_hi", "title": "गति के नियम - अध्याय परीक्षा", "description": "न्यूटन के गति के नियमों और उनके अनुप्रयोगों पर व्यापक प्रश्नोत्तरी", "subject": "भौतिक विज्ञान", "topic": "गति के नियम", "grade": "11", "language": "hi", "difficulty": "medium", "totalPoints": 25, "settings": {"timeLimit": 45, "attemptsAllowed": 2, "shuffleQuestions": true, "shuffleOptions": true, "showResults": "after_submission", "passingScore": 60}, "questions": [{"type": "mcq", "question": "न्यूटन का कौन सा नियम बताता है कि जब कार अचानक रुकती है तो यात्री आगे की ओर क्यों झुकता है?", "options": [{"text": "प्रथम नियम (जड़त्व का नियम)", "isCorrect": true}, {"text": "द्वितीय नियम (F = ma)", "isCorrect": false}, {"text": "तृतीय नियम (क्रिया-प्रतिक्रिया)", "isCorrect": false}, {"text": "गुरुत्वाकर्षण का नियम", "isCorrect": false}], "correctAnswer": "प्रथम नियम (जड़त्व का नियम)", "explanation": "प्रथम नियम कहता है कि गतिशील वस्तु तब तक गतिशील रहती है जब तक कोई बाहरी बल न लगे। यात्री जड़त्व के कारण आगे बढ़ता रहता है।", "points": 2, "difficulty": "easy", "tags": ["न्यूटन", "जड़त्व", "गति"]}, {"type": "mcq", "question": "यदि 4 kg द्रव्यमान की वस्तु पर 20 N का बल लगाया जाए तो उसका त्वरण क्या होगा?", "options": [{"text": "5 m/s²", "isCorrect": true}, {"text": "80 m/s²", "isCorrect": false}, {"text": "0.2 m/s²", "isCorrect": false}, {"text": "16 m/s²", "isCorrect": false}], "correctAnswer": "5 m/s²", "explanation": "न्यूटन के द्वितीय नियम का उपयोग करते हुए: F = ma, अतः a = F/m = 20N/4kg = 5 m/s²", "points": 3, "difficulty": "medium", "tags": ["न्यूटन", "बल", "त्वरण"]}], "tags": ["भौतिक विज्ञान", "गति", "न्यूटन", "कक्षा-11", "एनसीईआरटी"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}}, {"id": "quiz_chemistry_11_structure_en", "title": "Structure of Atom - Chapter Test", "description": "Quiz covering atomic structure, electron configuration, and quantum numbers", "subject": "Chemistry", "topic": "Structure of Atom", "grade": "11", "language": "en", "difficulty": "medium", "totalPoints": 20, "settings": {"timeLimit": 40, "attemptsAllowed": 2, "shuffleQuestions": true, "shuffleOptions": true, "showResults": "after_submission", "passingScore": 60}, "questions": [{"type": "mcq", "question": "Which scientist proposed the planetary model of the atom?", "options": [{"text": "<PERSON>", "isCorrect": true}, {"text": "Thomson", "isCorrect": false}, {"text": "<PERSON><PERSON>", "isCorrect": false}, {"text": "<PERSON>", "isCorrect": false}], "correctAnswer": "<PERSON>", "explanation": "<PERSON> proposed the planetary model after his gold foil experiment, showing that atoms have a dense nucleus with electrons orbiting around it.", "points": 2, "difficulty": "easy", "tags": ["atomic-models", "<PERSON><PERSON><PERSON><PERSON>"]}, {"type": "mcq", "question": "What is the maximum number of electrons that can be accommodated in the M shell?", "options": [{"text": "18", "isCorrect": true}, {"text": "8", "isCorrect": false}, {"text": "32", "isCorrect": false}, {"text": "2", "isCorrect": false}], "correctAnswer": "18", "explanation": "The M shell (n=3) can hold a maximum of 2n² = 2(3)² = 18 electrons.", "points": 3, "difficulty": "medium", "tags": ["electron-configuration", "shells"]}], "tags": ["chemistry", "atomic-structure", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}}, {"id": "quiz_math_11_trigonometry_en", "title": "Trigonometric Functions - Chapter Test", "description": "Quiz on trigonometric ratios, identities, and equations", "subject": "Mathematics", "topic": "Trigonometric Functions", "grade": "11", "language": "en", "difficulty": "medium", "totalPoints": 30, "settings": {"timeLimit": 50, "attemptsAllowed": 2, "shuffleQuestions": true, "shuffleOptions": true, "showResults": "after_submission", "passingScore": 60}, "questions": [{"type": "mcq", "question": "What is the value of sin(90°)?", "options": [{"text": "1", "isCorrect": true}, {"text": "0", "isCorrect": false}, {"text": "-1", "isCorrect": false}, {"text": "undefined", "isCorrect": false}], "correctAnswer": "1", "explanation": "sin(90°) = 1, as the y-coordinate of the point on the unit circle at 90° is 1.", "points": 2, "difficulty": "easy", "tags": ["trigonometry", "basic-ratios"]}, {"type": "mcq", "question": "Which of the following is the correct identity?", "options": [{"text": "sin²θ + cos²θ = 1", "isCorrect": true}, {"text": "sin²θ - cos²θ = 1", "isCorrect": false}, {"text": "sin²θ + cos²θ = 0", "isCorrect": false}, {"text": "sinθ + cosθ = 1", "isCorrect": false}], "correctAnswer": "sin²θ + cos²θ = 1", "explanation": "This is the fundamental trigonometric identity, known as the Pythagorean identity.", "points": 3, "difficulty": "medium", "tags": ["trigonometry", "identities"]}, {"type": "short_answer", "question": "What is the period of sin(x)?", "correctAnswer": "2π", "explanation": "The sine function repeats its values every 2π radians or 360°.", "points": 2, "difficulty": "medium", "tags": ["trigonometry", "period"]}], "tags": ["mathematics", "trigonometry", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}}, {"id": "quiz_biology_11_cell_en", "title": "Cell: The Unit of Life - Chapter Test", "description": "Quiz on cell structure, organelles, and cellular processes", "subject": "Biology", "topic": "Cell: The Unit of Life", "grade": "11", "language": "en", "difficulty": "medium", "totalPoints": 25, "settings": {"timeLimit": 45, "attemptsAllowed": 2, "shuffleQuestions": true, "shuffleOptions": true, "showResults": "after_submission", "passingScore": 60}, "questions": [{"type": "mcq", "question": "Which organelle is known as the 'powerhouse of the cell'?", "options": [{"text": "Mitochondria", "isCorrect": true}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "isCorrect": false}, {"text": "Ribosome", "isCorrect": false}, {"text": "Golgi apparatus", "isCorrect": false}], "correctAnswer": "Mitochondria", "explanation": "Mitochondria are called the powerhouse of the cell because they produce ATP through cellular respiration.", "points": 2, "difficulty": "easy", "tags": ["cell-biology", "organelles", "mitochondria"]}, {"type": "true_false", "question": "Plant cells have a cell wall but animal cells do not.", "correctAnswer": "true", "explanation": "Plant cells have a rigid cell wall made of cellulose, while animal cells only have a flexible cell membrane.", "points": 2, "difficulty": "easy", "tags": ["cell-biology", "plant-animal-cells"]}, {"type": "mcq", "question": "Which structure controls the entry and exit of materials in a cell?", "options": [{"text": "Cell membrane", "isCorrect": true}, {"text": "Cell wall", "isCorrect": false}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "isCorrect": false}, {"text": "Cytoplasm", "isCorrect": false}], "correctAnswer": "Cell membrane", "explanation": "The cell membrane is selectively permeable and controls what enters and exits the cell.", "points": 3, "difficulty": "medium", "tags": ["cell-biology", "membrane", "transport"]}], "tags": ["biology", "cell-biology", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}}, {"id": "quiz_economics_11_intro_en", "title": "Introduction to Economics - Chapter Test", "description": "Quiz on basic economic concepts, scarcity, and economic systems", "subject": "Economics", "topic": "Introduction to Economics", "grade": "11", "language": "en", "difficulty": "medium", "totalPoints": 20, "settings": {"timeLimit": 40, "attemptsAllowed": 2, "shuffleQuestions": true, "shuffleOptions": true, "showResults": "after_submission", "passingScore": 60}, "questions": [{"type": "mcq", "question": "What is the fundamental economic problem?", "options": [{"text": "Scarcity", "isCorrect": true}, {"text": "Inflation", "isCorrect": false}, {"text": "Unemployment", "isCorrect": false}, {"text": "Poverty", "isCorrect": false}], "correctAnswer": "Scarcity", "explanation": "Scarcity is the fundamental economic problem - unlimited wants but limited resources.", "points": 3, "difficulty": "medium", "tags": ["economics", "scarcity", "basic-concepts"]}, {"type": "short_answer", "question": "What are the three basic economic questions?", "correctAnswer": "What to produce, How to produce, For whom to produce", "explanation": "These are the three fundamental questions every economy must answer due to scarcity.", "points": 4, "difficulty": "medium", "tags": ["economics", "basic-questions"]}], "tags": ["economics", "introduction", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}}]}