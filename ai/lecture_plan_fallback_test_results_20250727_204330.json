[{"test_case": "Physics - English", "success": true, "plan_quality": {"score": 10, "max_score": 10, "percentage": 100.0, "feedback": ["Good title length", "Adequate number of objectives", "<PERSON>'s taxonomy levels specified", "Activities included", "Differentiation strategies included", "Resources provided", "Assessment methods included", "Complete lesson structure", "Detailed main content sections", "Multiple teaching strategies"]}, "validation": {"valid": true, "errors": []}}, {"test_case": "Mathematics - Hindi", "success": true, "plan_quality": {"score": 10, "max_score": 10, "percentage": 100.0, "feedback": ["Good title length", "Adequate number of objectives", "<PERSON>'s taxonomy levels specified", "Activities included", "Differentiation strategies included", "Resources provided", "Assessment methods included", "Complete lesson structure", "Detailed main content sections", "Multiple teaching strategies"]}, "validation": {"valid": true, "errors": []}}]