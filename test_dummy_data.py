#!/usr/bin/env python3
"""
Comprehensive test script for EduSarathi dummy data
Tests all modules with generated dummy datasets
"""

import json
import os
import sys
import requests
import time
from pathlib import Path

# Add the ai directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ai'))

def load_dummy_data():
    """Load all dummy data files"""
    dummy_data_dir = Path('ai/dummy_data')
    data = {}
    
    # Load all JSON files
    json_files = ['quizzes.json', 'slides.json', 'mindmaps.json', 'lecture_plans.json', 'curriculum.json']
    
    for file_name in json_files:
        file_path = dummy_data_dir / file_name
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data[file_name.replace('.json', '')] = json.load(f)
                print(f"✓ Loaded {file_name}")
            except json.JSONDecodeError as e:
                print(f"✗ Error loading {file_name}: {e}")
                return None
        else:
            print(f"✗ File not found: {file_path}")
            return None
    
    return data

def validate_quiz_data(quizzes_data):
    """Validate quiz data structure"""
    print("\n=== Validating Quiz Data ===")
    
    if 'quizzes' not in quizzes_data:
        print("✗ No 'quizzes' key found")
        return False
    
    quizzes = quizzes_data['quizzes']
    print(f"Found {len(quizzes)} quizzes")
    
    for i, quiz in enumerate(quizzes):
        # Check required fields
        required_fields = ['id', 'title', 'subject', 'topic', 'grade', 'questions']
        for field in required_fields:
            if field not in quiz:
                print(f"✗ Quiz {i}: Missing required field '{field}'")
                return False
        
        # Check questions structure
        if not quiz['questions']:
            print(f"✗ Quiz {i}: No questions found")
            return False
        
        for j, question in enumerate(quiz['questions']):
            if 'type' not in question or 'question' not in question:
                print(f"✗ Quiz {i}, Question {j}: Missing type or question text")
                return False
        
        print(f"✓ Quiz {i}: {quiz['title']} - {len(quiz['questions'])} questions")
    
    return True

def validate_slides_data(slides_data):
    """Validate slide deck data structure"""
    print("\n=== Validating Slides Data ===")
    
    if 'slideDecks' not in slides_data:
        print("✗ No 'slideDecks' key found")
        return False
    
    slide_decks = slides_data['slideDecks']
    print(f"Found {len(slide_decks)} slide decks")
    
    for i, deck in enumerate(slide_decks):
        # Check required fields
        required_fields = ['id', 'title', 'subject', 'topic', 'slides']
        for field in required_fields:
            if field not in deck:
                print(f"✗ Slide deck {i}: Missing required field '{field}'")
                return False
        
        # Check slides structure
        if not deck['slides']:
            print(f"✗ Slide deck {i}: No slides found")
            return False
        
        for j, slide in enumerate(deck['slides']):
            if 'slideNumber' not in slide or 'title' not in slide:
                print(f"✗ Slide deck {i}, Slide {j}: Missing slideNumber or title")
                return False
        
        print(f"✓ Slide deck {i}: {deck['title']} - {len(deck['slides'])} slides")
    
    return True

def validate_mindmaps_data(mindmaps_data):
    """Validate mind map data structure"""
    print("\n=== Validating MindMaps Data ===")
    
    if 'mindMaps' not in mindmaps_data:
        print("✗ No 'mindMaps' key found")
        return False
    
    mindmaps = mindmaps_data['mindMaps']
    print(f"Found {len(mindmaps)} mind maps")
    
    for i, mindmap in enumerate(mindmaps):
        # Check required fields
        required_fields = ['id', 'title', 'subject', 'topic', 'nodes', 'edges']
        for field in required_fields:
            if field not in mindmap:
                print(f"✗ Mind map {i}: Missing required field '{field}'")
                return False
        
        # Check nodes and edges
        if not mindmap['nodes']:
            print(f"✗ Mind map {i}: No nodes found")
            return False
        
        for j, node in enumerate(mindmap['nodes']):
            if 'id' not in node or 'label' not in node:
                print(f"✗ Mind map {i}, Node {j}: Missing id or label")
                return False
        
        print(f"✓ Mind map {i}: {mindmap['title']} - {len(mindmap['nodes'])} nodes, {len(mindmap['edges'])} edges")
    
    return True

def validate_lecture_plans_data(lecture_plans_data):
    """Validate lecture plans data structure"""
    print("\n=== Validating Lecture Plans Data ===")
    
    if 'lecture_plans' not in lecture_plans_data:
        print("✗ No 'lecture_plans' key found")
        return False
    
    lecture_plans = lecture_plans_data['lecture_plans']
    print(f"Found {len(lecture_plans)} lecture plans")
    
    for i, plan in enumerate(lecture_plans):
        # Check required fields
        required_fields = ['id', 'title', 'subject', 'topic', 'grade', 'duration']
        for field in required_fields:
            if field not in plan:
                print(f"✗ Lecture plan {i}: Missing required field '{field}'")
                return False
        
        print(f"✓ Lecture plan {i}: {plan['title']} - {plan['duration']} minutes")
    
    return True

def validate_curriculum_data(curriculum_data):
    """Validate curriculum data structure"""
    print("\n=== Validating Curriculum Data ===")
    
    if 'curricula' not in curriculum_data:
        print("✗ No 'curricula' key found")
        return False
    
    curricula = curriculum_data['curricula']
    print(f"Found {len(curricula)} curricula")
    
    for i, curriculum in enumerate(curricula):
        # Check required fields
        required_fields = ['id', 'title', 'subject', 'grade', 'topics']
        for field in required_fields:
            if field not in curriculum:
                print(f"✗ Curriculum {i}: Missing required field '{field}'")
                return False
        
        print(f"✓ Curriculum {i}: {curriculum['title']} - {len(curriculum['topics'])} topics")
    
    return True

def test_subject_coverage(data):
    """Test that all subjects are covered across modules"""
    print("\n=== Testing Subject Coverage ===")
    
    expected_subjects = ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Economics']
    expected_subjects_hi = ['भौतिक विज्ञान', 'रसायन विज्ञान', 'गणित', 'जीव विज्ञान']
    
    all_subjects = set()
    
    # Collect subjects from all modules
    for module_name, module_data in data.items():
        if module_name == 'quizzes':
            for quiz in module_data.get('quizzes', []):
                all_subjects.add(quiz.get('subject', ''))
        elif module_name == 'slides':
            for deck in module_data.get('slideDecks', []):
                all_subjects.add(deck.get('subject', ''))
        elif module_name == 'mindmaps':
            for mindmap in module_data.get('mindMaps', []):
                all_subjects.add(mindmap.get('subject', ''))
        elif module_name == 'lecture_plans':
            for plan in module_data.get('lecture_plans', []):
                all_subjects.add(plan.get('subject', ''))
        elif module_name == 'curriculum':
            for curriculum in module_data.get('curricula', []):
                all_subjects.add(curriculum.get('subject', ''))
    
    print(f"Found subjects: {sorted(all_subjects)}")
    
    # Check coverage
    covered_en = sum(1 for subj in expected_subjects if subj in all_subjects)
    covered_hi = sum(1 for subj in expected_subjects_hi if subj in all_subjects)
    
    print(f"✓ English subjects covered: {covered_en}/{len(expected_subjects)}")
    print(f"✓ Hindi subjects covered: {covered_hi}/{len(expected_subjects_hi)}")
    
    return covered_en >= 3  # At least 3 subjects should be covered

def main():
    """Main test function"""
    print("🚀 Starting EduSarathi Dummy Data Validation")
    print("=" * 50)
    
    # Load dummy data
    data = load_dummy_data()
    if not data:
        print("❌ Failed to load dummy data")
        return False
    
    # Validate each module
    validations = [
        validate_quiz_data(data.get('quizzes', {})),
        validate_slides_data(data.get('slides', {})),
        validate_mindmaps_data(data.get('mindmaps', {})),
        validate_lecture_plans_data(data.get('lecture_plans', {})),
        validate_curriculum_data(data.get('curriculum', {})),
        test_subject_coverage(data)
    ]
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(validations)
    total = len(validations)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("🎉 Dummy data is ready for use!")
        return True
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("🔧 Please fix the issues before proceeding")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
